# 测试目录

本目录包含自动升级测试项目的所有测试文件。

## 测试文件列表

### 单元测试

- **[test_device_wait.py](test_device_wait.py)** - 设备等待功能测试
  - 测试设备连接检查功能
  - 测试等待设备连接功能
  - 测试文件推送重试机制

## 运行测试

### 运行单个测试

```bash
# 从项目根目录运行
python tests/test_device_wait.py
```

### 运行所有测试

```bash
# 从项目根目录运行
python -m pytest tests/
```

## 测试规范

1. **文件命名**: 测试文件以 `test_` 开头
2. **导入路径**: 测试文件应正确设置项目根目录路径
3. **独立性**: 每个测试文件应能独立运行
4. **日志**: 测试应输出详细的日志信息便于调试

## 添加新测试

1. 在此目录下创建新的测试文件
2. 确保正确设置导入路径
3. 添加适当的日志配置
4. 更新本README文件

## 注意事项

- 测试文件中的导入路径已配置为从项目根目录导入
- 运行测试前请确保项目依赖已正确安装
- 某些测试可能需要实际的硬件设备连接
