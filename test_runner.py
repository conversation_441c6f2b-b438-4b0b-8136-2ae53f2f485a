import json
import os
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

from loguru import logger

from adb_utils import AdbUtils
from report_generator import ReportGenerator
from serial_monitor import SerialMonitor


class TestRunner:
    def __init__(
        self, config_path="config.json"
    ):  # 保持为config.json，因为main.py会确保其存在
        self.config = self._load_config(config_path)
        self.adb_utils = AdbUtils(
            adb_host=self.config["adb_config"]["adb_host"],
            adb_port=self.config["adb_config"]["adb_port"],
            adb_serial=self.config["adb_config"]["adb_serial"],
            adb_connect_timeout=self.config["adb_config"]["adb_connect_timeout"],
            adb_reconnect_interval=self.config["adb_config"]["adb_reconnect_interval"],
        )
        self.serial_monitor = None  # 延迟初始化
        self.report_generator = None  # 延迟初始化
        self.test_results = []
        self.program_log_path = None  # 由main.py设置
        self.overall_start_time = None  # 初始化为None，在run_tests中设置
        self.output_dir = None  # 初始化为None，在run_tests中设置

        # 注册SIGINT信号处理器
        signal.signal(signal.SIGINT, self._handle_sigint)
        logger.info("已注册SIGINT信号处理器。")

    def _handle_sigint(self, signum, frame):
        """SIGINT信号处理器"""
        logger.warning("检测到Ctrl+C中断，正在保存测试结果并退出...")
        self._save_results_and_exit(interrupted=True)

    def _save_results_and_exit(self, interrupted=False):
        """保存测试结果并退出"""
        overall_end_time = datetime.now()

        # 停止串口监控
        if self.serial_monitor and self.serial_monitor.running:  # 使用.running属性
            self.serial_monitor.stop_monitoring()
            logger.info("串口监控已停止。")

        # 生成报告
        if self.report_generator:
            program_log_path = self.program_log_path if self.program_log_path else "N/A"
            mcu_log_file_path = (
                self.serial_monitor.log_file_path
                if self.serial_monitor and self.serial_monitor.log_file_path
                else "N/A"
            )
            overall_start_time = (
                self.overall_start_time if self.overall_start_time else overall_end_time
            )  # 如果没有设置开始时间，则使用结束时间

            logger.info("正在生成测试报告...")
            self.report_generator.generate_report(
                self.test_results,
                overall_start_time,
                overall_end_time,
                program_log_path,
                mcu_log_file_path,
                interrupted=interrupted,
            )
            logger.info("测试报告已生成。")
        else:
            logger.warning("报告生成器未初始化，无法生成报告。")

        sys.exit(1)  # 退出程序

    def _load_config(self, config_path):
        """加载配置文件"""
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        logger.info(f"成功加载配置文件: {config_path}")

        # 确保tbox_ota_path包含文件名
        if not config["test_config"]["tbox_ota_path"].endswith(
            "upgrade.zip"
        ):  # 修改这里，使用局部变量config
            config["test_config"]["tbox_ota_path"] = os.path.join(
                config["test_config"]["tbox_ota_path"], "upgrade.zip"
            ).replace("\\", "/")
            logger.warning(
                f"tbox_ota_path已自动补全为: {config['test_config']['tbox_ota_path']}"
            )

        return config

    def _pre_check(self):
        """执行测试前的预检查"""
        logger.info("执行测试前预检查...")

        # 检查本地升级包是否存在
        local_zip_path = Path(self.config["test_config"]["local_upgrade_zip_path"])
        if not local_zip_path.exists():
            logger.error(f"本地升级包不存在: {local_zip_path.as_posix()}")
            return False
        logger.info(f"本地升级包 {local_zip_path.as_posix()} 存在。")

        # 尝试ADB连接
        logger.info("尝试ADB连接...")
        if not self.adb_utils.connect():
            logger.error("初始ADB连接失败，请检查设备连接和ADB服务。")
            return False
        logger.info("初始ADB连接成功。")

        # 确保远程OTA目录存在，不存在则创建
        remote_ota_dir = os.path.dirname(self.config["test_config"]["tbox_ota_path"])
        logger.info(f"检查并创建远程OTA目录 {remote_ota_dir}...")
        if not self.adb_utils.remote_dir_exists(remote_ota_dir):
            logger.warning(f"远程OTA目录 {remote_ota_dir} 不存在，尝试创建。")
            if not self.adb_utils.create_remote_dir(remote_ota_dir):
                logger.error(f"创建远程OTA目录 {remote_ota_dir} 失败。")
                return False
        logger.info(f"远程OTA目录 {remote_ota_dir} 存在或已成功创建。")

        return True

    def _run_single_upgrade_test(self, iteration):
        """执行单次升级测试"""
        logger.info(f"--- 开始第 {iteration} 次升级测试 ---")
        test_start_time = datetime.now()
        success = False
        error_message = ""

        try:
            # 阶段1: 推送升级包并等待首次ADB断开
            logger.info("--- 阶段1: 推送升级包并等待首次ADB断开 ---")
            remote_zip_full_path = self.config["test_config"]["tbox_ota_path"]
            local_zip_path = self.config["test_config"]["local_upgrade_zip_path"]

            logger.info("1.1 检查并删除旧升级包...")
            if self.adb_utils.remote_file_exists(remote_zip_full_path):
                logger.warning(
                    f"TBOX上存在旧的升级包 {remote_zip_full_path}，尝试删除。"
                )
                if not self.adb_utils.delete_remote_file(remote_zip_full_path):
                    error_message = "阶段1失败: 删除旧升级包失败"
                    logger.error(error_message)
                    return False, 0, test_start_time, datetime.now(), error_message
            logger.info("1.1 旧升级包已处理。")

            logger.info(f"1.2 推送升级包 {Path(local_zip_path).name}...")

            # 在推送前先确保设备已连接，等待设备启动
            logger.info("1.2.1 等待设备连接...")
            if not self.adb_utils.wait_for_device(timeout=120):  # 等待2分钟设备连接
                error_message = "阶段1失败: 等待设备连接超时，设备可能未启动"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("1.2.1 设备已连接。")

            push_success, push_output = self.adb_utils.push_file(
                local_zip_path, remote_zip_full_path
            )
            if not push_success:
                error_message = f"阶段1失败: 推送升级包失败: {push_output}"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("1.2 升级包推送完成。")

            logger.info("1.3 校验MD5...")
            md5_match, md5_msg = self.adb_utils.compare_file_md5(
                local_zip_path, remote_zip_full_path
            )
            if not md5_match:
                error_message = f"阶段1失败: 推送后MD5校验失败: {md5_msg}"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("1.3 升级包MD5校验成功。")

            push_complete_time = datetime.now()  # 升级开始计时点

            logger.info("1.4 等待ADB首次断开 (TBOX开始升级)...")
            disconnect_timeout = 60  # 等待断开的超时时间
            start_wait_disconnect = time.time()
            while self.adb_utils.is_connected() and (
                time.time() - start_wait_disconnect < disconnect_timeout
            ):
                logger.info(
                    f"1.4 等待ADB首次断开 (TBOX开始升级)... {int(time.time() - start_wait_disconnect)}s"
                )
                time.sleep(self.adb_utils.adb_reconnect_interval)

            if self.adb_utils.is_connected():
                error_message = (
                    "阶段1失败: ADB连接未断开，可能TBOX未开始升级或断开超时。"
                )
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("1.4 ADB连接已首次断开。")

            # 阶段2: ARM升级流程并等待二次ADB断开
            logger.info("--- 阶段2: ARM升级流程并等待二次ADB断开 ---")
            logger.info("2.1 等待ADB首次重连 (ARM重启)...")
            if not self.adb_utils.wait_for_device(timeout=150):  # 等待重连的超时时间
                error_message = "阶段2失败: ADB首次重连接超时。"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("2.1 ADB已首次重连接。")

            logger.info("2.3 等待ADB二次断开 (ARM再次被MCU复位)...")
            disconnect_timeout_2 = 180  # 等待二次断开的超时时间
            start_wait_disconnect_2 = time.time()
            while self.adb_utils.is_connected() and (
                time.time() - start_wait_disconnect_2 < disconnect_timeout_2
            ):
                logger.info(
                    f"2.3 等待ADB二次断开... {int(time.time() - start_wait_disconnect_2)}s"
                )
                time.sleep(self.adb_utils.adb_reconnect_interval)

            if self.adb_utils.is_connected():
                error_message = (
                    "阶段2失败: ADB连接未二次断开，可能ARM未被复位或断开超时。"
                )
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("2.3 ADB连接已二次断开。")

            # 阶段3: 升级成功判断
            logger.info("--- 阶段3: 升级成功判断 ---")
            logger.info("3.1 等待ADB二次重连 (ARM再次重启)...")
            if not self.adb_utils.wait_for_device(timeout=150):  # 等待重连的超时时间
                error_message = "阶段3失败: ADB二次重连接超时。"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            logger.info("3.1 ADB已二次重连接。")

            logger.info(
                f"3.2 循环检查升级包 {Path(remote_zip_full_path).name} 是否被清理..."
            )
            package_cleaned = False
            check_package_timeout = 300  # 等待升级包清理的超时时间
            start_check_package = time.time()
            while time.time() - start_check_package < check_package_timeout:
                # 每次循环都检查ADB连接状态，如果断开，则认为已清理并退出循环
                if not self.adb_utils.is_connected():
                    package_cleaned = True  # 视为已清理
                    break

                if not self.adb_utils.remote_file_exists(remote_zip_full_path):
                    package_cleaned = True
                    break
                logger.info(
                    f"3.2 循环检查升级包是否被清理... {int(time.time() - start_check_package)}s"
                )
                time.sleep(self.adb_utils.adb_reconnect_interval)

            if not package_cleaned:
                error_message = "阶段3失败: 升级包长时间未被清理，可能升级失败。"
                logger.error(error_message)
                return False, 0, test_start_time, datetime.now(), error_message
            else:
                # 根据ADB连接状态输出不同的日志
                if not self.adb_utils.is_connected():
                    logger.info(
                        "3.2 ADB连接已断开，无法检查升级包是否清理，但视为已清理。"
                    )
                else:
                    logger.info("3.2 远程升级包已清理。")

            # 3.3 最终检查远程升级包是否已消失的逻辑已合并到 3.2 阶段
            # 如果代码执行到这里，说明 3.2 阶段已经确认升级包已被清理
            logger.info("3.3 远程升级包已消失，升级成功。")

            success = True
            test_end_time = datetime.now()
            duration = (test_end_time - push_complete_time).total_seconds()
            logger.info(f"第 {iteration} 次升级测试成功，总耗时: {duration:.2f} 秒。")
            return success, duration, test_start_time, test_end_time, None

        except Exception as e:
            error_message = f"测试异常: {str(e)}"
            logger.error(f"第 {iteration} 次升级测试发生异常: {e}", exc_info=True)
            return False, 0, test_start_time, datetime.now(), error_message
        finally:
            # 确保每次测试结束时ADB连接是断开的，避免影响下一次测试
            if self.adb_utils.is_connected():
                self.adb_utils.disconnect()
            logger.info(f"--- 第 {iteration} 次升级测试结束 ---")

    def run_tests(self):
        """运行所有升级测试"""
        logger.info("--- MCU 自动升级测试开始 ---")
        self.overall_start_time = datetime.now()  # 设置整体开始时间

        # 设置程序日志路径
        # 确保output_dir存在，并在其中创建本次测试的专属目录
        test_session_dir_name = self.overall_start_time.strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(self.config["output_dir"], test_session_dir_name)
        os.makedirs(self.output_dir, exist_ok=True)

        # 重新配置loguru，将程序日志输出到本次测试的专属目录
        from logger_config import setup_logging

        self.program_log_path = setup_logging(self.output_dir, self.config)
        logger.info(
            f"本次测试结果和日志将保存到: {self.output_dir.replace(os.sep, '/')}"
        )

        # 重新初始化ReportGenerator，确保报告生成到正确的output_dir
        self.report_generator = ReportGenerator(self.output_dir)

        # 启动串口监控
        self.serial_monitor = SerialMonitor(
            port=self.config["serial_config"]["serial_port"],
            baudrate=self.config["serial_config"]["serial_baudrate"],
            bytesize=self.config["serial_config"]["serial_bytesize"],
            parity=self.config["serial_config"]["serial_parity"],
            stopbits=self.config["serial_config"]["serial_stopbits"],
            timeout=self.config["serial_config"]["serial_timeout"],
            log_dir=Path(self.output_dir),  # MCU日志直接存放在output_dir下
        )
        self.serial_monitor.log_dir.mkdir(parents=True, exist_ok=True)  # Use Path.mkdir
        self.serial_monitor.start_monitoring()

        if not self._pre_check():
            logger.error("预检查失败，测试终止。")
            self._save_results_and_exit(interrupted=True)  # 预检查失败也保存报告
            return

        for i in range(1, self.config["test_config"]["test_iterations"] + 1):
            logger.info(
                f"正在执行第 {i}/{self.config['test_config']['test_iterations']} 次升级测试..."
            )
            success, duration, single_test_start, single_test_end, error_msg = (
                self._run_single_upgrade_test(i)
            )
            self.test_results.append(
                {
                    "iteration": i,
                    "success": success,
                    "duration": duration,
                    "start_time": single_test_start,
                    "end_time": single_test_end,
                    "error_message": error_msg,
                }
            )
            # 如果阶段3失败，直接退出整个测试
            if not success and "阶段3失败" in error_msg:
                logger.error(f"第 {i} 次升级测试阶段3失败，终止后续测试。")
                break  # 退出循环

            # 每次测试后断开ADB，确保下次测试从干净状态开始
            if self.adb_utils.is_connected():
                self.adb_utils.disconnect()
            time.sleep(5)  # 每次测试间隔，给设备一些时间稳定

        logger.info("--- MCU 自动升级测试结束 ---")
        self._save_results_and_exit(interrupted=False)  # 正常结束也保存报告


if __name__ == "__main__":
    # 示例用法
    # 在运行前，请确保config.json已正确配置
    # 并且ADB服务已启动，设备已连接
    # 例如：adb tcpip 5555; adb connect *************:5555
    # 并且本地有upgrade.zip文件

    # 注意：这里的日志配置会被TestRunner内部的日志配置覆盖，因为TestRunner会根据output_dir重新设置日志
    # 所以这里只是为了让示例代码能正常运行
    import tempfile

    from logger_config import setup_logging

    temp_output_dir = Path(os.path.join(tempfile.gettempdir(), "autoupgrade_test_run"))
    temp_output_dir.mkdir(parents=True, exist_ok=True)

    # 加载config.json
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
    except FileNotFoundError:
        print("config.json not found. Using default logging levels.")
        config = {}  # 或者加载config.json.template作为默认配置
    except json.JSONDecodeError:
        print("Error decoding config.json. Using default logging levels.")
        config = {}

    setup_logging(temp_output_dir.as_posix(), config)

    runner = TestRunner(config_path="config.json")
    runner.run_tests()
