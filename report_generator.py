import time
from datetime import datetime
from pathlib import Path

from loguru import logger


class ReportGenerator:
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)  # Convert to Path object
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"报告生成器初始化，报告将输出到: {self.output_dir.as_posix()}")

    def generate_report(
        self,
        test_results,
        start_time,
        end_time,
        program_log_path,
        mcu_log_path=None,
        interrupted=False,
    ):
        """
        生成测试报告。
        test_results: 包含每次升级测试结果的列表。
                      每个元素是一个字典，包含 'duration', 'success', 'start_time', 'end_time'
        start_time: 整个测试的开始时间。
        end_time: 整个测试的结束时间。
        program_log_path: 程序日志文件的路径。
        mcu_log_path: MCU日志文件的路径 (可选)。
        interrupted: 布尔值，指示测试是否被中断。
        """
        total_tests = len(test_results)
        successful_tests = sum(1 for r in test_results if r["success"])

        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0

        durations = [r["duration"] for r in test_results if r["success"]]

        max_duration = 0
        min_duration = float("inf")
        avg_duration = 0
        max_duration_info = {"time": None, "iteration": None}
        min_duration_info = {"time": None, "iteration": None}

        if durations:
            max_duration = max(durations)
            min_duration = min(durations)
            avg_duration = sum(durations) / len(durations)

            # 找到最大耗时和最小耗时对应的测试信息
            for r in test_results:
                if r["success"]:
                    if r["duration"] == max_duration:
                        max_duration_info["time"] = r["start_time"].strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                        max_duration_info["iteration"] = r["iteration"]
                    if r["duration"] == min_duration:
                        min_duration_info["time"] = r["start_time"].strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                        min_duration_info["iteration"] = r["iteration"]

        report_file_name = datetime.now().strftime("%Y%m%d_%H%M%S_test_report.txt")
        report_file_path = self.output_dir / report_file_name

        with open(report_file_path.as_posix(), "w", encoding="utf-8") as f:
            f.write("--- MCU 自动升级测试报告 ---\n")
            f.write(f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            if interrupted:
                f.write("测试状态: 被用户中断\n")
            else:
                f.write("测试状态: 正常完成\n")
            f.write(f"总测试次数: {total_tests}\n")
            f.write(f"成功次数: {successful_tests}\n")
            f.write(f"成功率: {success_rate:.2f}%\n")
            f.write("\n--- 耗时统计 (仅限成功升级) ---\n")
            f.write(
                f"单次最大耗时: {max_duration:.2f} 秒 (发生时间: {max_duration_info['time'] if max_duration_info['time'] else 'N/A'}, 第 {max_duration_info['iteration']} 次测试)\n"
            )
            f.write(
                f"单次最短耗时: {min_duration:.2f} 秒 (发生时间: {min_duration_info['time'] if min_duration_info['time'] else 'N/A'}, 第 {min_duration_info['iteration']} 次测试)\n"
            )
            f.write(f"平均耗时: {avg_duration:.2f} 秒\n")
            f.write("\n--- 每次升级详情 ---\n")
            for i, result in enumerate(test_results):
                status = "成功" if result["success"] else "失败"
                f.write(
                    f"  第 {i + 1} 次升级: 状态={status}, 耗时={result['duration']:.2f} 秒, 开始时间={result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}, 结束时间={result['end_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
                )
                if not result["success"] and "error_message" in result:
                    f.write(f"    失败原因: {result['error_message']}\n")

            f.write("\n--- 日志文件路径 ---\n")
            f.write(f"程序日志: {Path(program_log_path).as_posix()}\n")
            if mcu_log_path:
                f.write(f"MCU日志: {Path(mcu_log_path).as_posix()}\n")
            f.write("\n-----------------------------------\n")

        logger.info(f"测试报告已生成: {report_file_path.as_posix()}")
        return report_file_path.as_posix()


if __name__ == "__main__":
    # 示例用法
    import tempfile
    import json  # 导入json模块

    from logger_config import setup_logging

    # 创建一个临时日志目录
    temp_output_dir = Path(tempfile.gettempdir()) / "report_test_output"
    temp_output_dir.mkdir(parents=True, exist_ok=True)
    # 加载config.json，用于setup_logging
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
    except FileNotFoundError:
        print("config.json not found. Using default logging levels.")
        config = {}
    except json.JSONDecodeError:
        print("Error decoding config.json. Using default logging levels.")
        config = {}
    program_log_file = setup_logging(temp_output_dir.as_posix(), config)

    reporter = ReportGenerator(temp_output_dir.as_posix())

    test_start_time = datetime.now()
    time.sleep(1)  # 模拟测试运行
    test_end_time = datetime.now()

    mock_results = [
        {
            "duration": 10.5,
            "success": True,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
        },
        {
            "duration": 12.1,
            "success": True,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
        },
        {
            "duration": 8.9,
            "success": True,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
        },
        {
            "duration": 15.0,
            "success": False,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "error_message": "ADB连接失败",
        },
        {
            "duration": 11.2,
            "success": True,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
        },
    ]

    # 模拟MCU日志路径
    mock_mcu_log_path = temp_output_dir / "mock_mcu_log.txt"
    with open(mock_mcu_log_path.as_posix(), "w") as f:
        f.write("Mock MCU log content.\n")

    report_path = reporter.generate_report(
        test_results=mock_results,
        start_time=test_start_time,
        end_time=test_end_time,
        program_log_path=program_log_file,
        mcu_log_path=mock_mcu_log_path.as_posix(),
    )
    logger.info(f"生成的报告路径: {report_path}")
