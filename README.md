# MCU 自动升级测试工具

这是一个用于自动化MCU（微控制器单元）反复升级测试的Python脚本。它通过ADB（Android Debug Bridge）与TBOX（车载信息终端）进行交互，监控升级过程中的串口日志，并生成详细的测试报告。

## 功能特性

*   **ADB集成**：
    *   通过ADB连接TBOX，支持USB和TCP/IP两种连接方式。
    *   支持通过设备序列号指定目标设备。
    *   自动检查并创建远程OTA（Over-The-Air）升级包存放目录。
    *   推送本地升级包到TBOX指定路径。
    *   校验本地与远程升级包的MD5值，确保文件完整性。
    *   监控ADB连接的断开与重连，以判断TBOX的升级状态。
*   **串口日志监控**：
    *   全程监控用户指定的串口输出（MCU日志），并将其保存到独立文件中。
    *   支持配置串口参数（波特率、数据位、校验位、停止位等）。
*   **自动化升级流程**：
    *   实现三阶段升级流程：
        1.  推送升级包并等待首次ADB断开（ARM通知MCU升级，MCU复位ARM）。
        2.  ARM升级流程并等待二次ADB断开（ARM重启进入升级，清理升级包后再次被MCU复位）。
        3.  升级成功判断（ARM再次重启，最终确认升级包已消失）。
    *   每个阶段的关键步骤和失败都会有详细的日志记录。
*   **详细日志记录**：
    *   使用 `loguru` 库记录程序运行期间的所有日志，支持DEBUG级别输出。
    *   程序日志和MCU日志按每次测试会话独立存放，方便追溯。
*   **自动化报告生成**：
    *   测试完成后自动生成包含关键指标的报告。
    *   报告内容包括：成功次数/总次数、成功率、单次最大/最短/平均耗时（包含发生时间及迭代次数）、测试开始/结束时间。
    *   报告中引用的日志文件路径为相对路径，方便分享和查看。
*   **优雅退出**：支持用户在测试过程中通过 `Ctrl+C` 优雅中断程序，并进行清理和报告生成。

## 项目结构

```
.
├── config.json.template  # 配置文件模板
├── .gitignore            # Git忽略文件
├── main.py               # 程序入口
├── logger_config.py      # 日志配置模块
├── adb_utils.py          # ADB操作封装模块
├── serial_monitor.py     # 串口监控模块
├── test_runner.py        # 核心测试逻辑模块
├── report_generator.py   # 报告生成模块
├── pyproject.toml        # uv/pip项目配置
├── uv.lock               # uv锁文件
└── test_results/         # 测试结果输出目录 (运行时自动创建)
    └── YYYYMMDD_HHMMSS/  # 每次测试会话的独立目录
        ├── YYYYMMDD_HHMMSS_test_log.log  # 程序运行日志
        ├── mcu_logs/                     # MCU日志目录
        │   └── YYYYMMDD_HHMMSS_mcu_log.txt # MCU串口日志
        └── YYYYMMDD_HHMMSS_test_report.txt # 测试报告
```

## 安装与配置

### 1. 环境准备

确保您的系统已安装 Python 3.8+ 和 ADB 工具。ADB工具需要添加到系统PATH中，以便脚本能够直接调用 `adb` 命令。

### 2. 安装依赖

推荐使用 `uv` 进行包管理：

```bash
# 如果未安装uv，请先安装：
# pip install uv

# 在项目根目录安装所有依赖
uv sync
```

### 3. 配置 `config.json`

1.  **复制配置文件模板：**
    将 `config.json.template` 复制并重命名为 `config.json`。
    ```bash
    cp config.json.template config.json  # Linux/macOS
    copy config.json.template config.json # Windows
    ```
2.  **编辑 `config.json`：**
    打开 `config.json` 文件，根据您的实际环境修改以下参数：

    ```json5
    {
        "output_dir": "./test_results",      // 所有测试结果和日志的输出根目录
        "adb_config": {
            "adb_host": "",                     // TBOX的IP地址 (TCP/IP连接时填写，USB连接时留空)
            "adb_port": 0,                      // TBOX上ADB服务监听的端口 (TCP/IP连接时填写，USB连接时设为0)
            "adb_serial": "",                   // TBOX的ADB序列号 (USB连接且有多个设备时填写，否则留空)
            "adb_connect_timeout": 10,          // ADB连接超时时间 (秒)
            "adb_reconnect_interval": 5         // ADB重连尝试间隔 (秒)
        },
        "serial_config": {
            "serial_port": "COM1",              // MCU串口名称 (例如 Windows: COM1, Linux: /dev/ttyUSB0)
            "serial_baudrate": 115200,          // 串口波特率
            "serial_bytesize": 8,               // 串口数据位 (5, 6, 7, 8)
            "serial_parity": "N",               // 串口奇偶校验 (N, E, O, M, S)
            "serial_stopbits": 1,               // 串口停止位 (1, 1.5, 2)
            "serial_timeout": 1                 // 串口读取超时时间 (秒)
        },
        "test_config": {
            "local_upgrade_zip_path": "D:/path/to/your/upgrade.zip", // 本地 upgrade.zip 文件的绝对路径
            "test_iterations": 3,               // 反复升级的次数
            "tbox_ota_path": "/tbox/ota/upgrade.zip"  // TBOX上OTA包的完整路径，包含文件名 (例如 /tbox/ota/upgrade.zip)
        },
        "logging": {
            "file_level": "DEBUG",              // 文件日志级别 (可选: DEBUG, INFO, WARNING, ERROR, CRITICAL)
            "console_level": "INFO"             // 控制台日志级别 (可选: DEBUG, INFO, WARNING, ERROR, CRITICAL)
        }
    }
    ```

    **配置说明：**

    *   **`output_dir`**: 所有测试结果和日志的输出根目录。
    *   **`adb_config`**: ADB 连接相关配置。
        *   `adb_host`: TBOX的IP地址 (TCP/IP连接时填写，USB连接时留空)。
        *   `adb_port`: TBOX上ADB服务监听的端口 (TCP/IP连接时填写，USB连接时设为0)。
        *   `adb_serial`: TBOX的ADB序列号 (USB连接且有多个设备时填写，否则留空)。
        *   `adb_connect_timeout`: ADB连接超时时间 (秒)。
        *   `adb_reconnect_interval`: ADB重连尝试间隔 (秒)。
        *   **ADB连接方式说明：**
            *   **通过USB连接（自动选择）：** `adb_host` 留空 `""`，`adb_port` 设为 `0`，`adb_serial` 留空 `""`。脚本将尝试自动识别唯一连接的USB设备。
            *   **通过USB连接（指定序列号）：** `adb_host` 留空 `""`，`adb_port` 设为 `0`，`adb_serial` 设置为您的TBOX的ADB序列号（例如：`"1234567890ABCDEF"`）。当有多个USB设备连接时，推荐使用此方式。
            *   **通过网络（TCP/IP）连接：** `adb_host` 设置为TBOX的IP地址（例如：`"*************"`），`adb_port` 设置为TBOX上ADB服务监听的端口（例如：`5555`），`adb_serial` 留空 `""`。
    *   **`serial_config`**: 串口监控相关配置。
        *   `serial_port`: MCU串口名称 (例如 Windows: COM1, Linux: /dev/ttyUSB0)。
        *   `serial_baudrate`: 串口波特率。
        *   `serial_bytesize`: 串口数据位 (5, 6, 7, 8)。
        *   `serial_parity`: 串口奇偶校验 (N, E, O, M, S)。
        *   `serial_stopbits`: 串口停止位 (1, 1.5, 2)。
        *   `serial_timeout`: 串口读取超时时间 (秒)。
    *   **`test_config`**: 升级测试流程相关配置。
        *   `local_upgrade_zip_path`: 本地 `upgrade.zip` 文件的绝对路径。
        *   `test_iterations`: 反复升级的次数。
        *   `tbox_ota_path`: TBOX上OTA包的完整路径，包含文件名 (例如 `/tbox/ota/upgrade.zip`)。
    *   **`logging`**: 日志输出级别配置。
        *   `file_level`: 程序日志文件输出的最低级别。
        *   `console_level`: 程序日志控制台输出的最低级别。

### 4. 确保ADB连接

*   确保您的TBOX已通过USB或网络正确连接到电脑。
*   确保TBOX上的ADB调试模式已开启，并且ADB服务已启动并监听在正确的IP和端口上。
*   您可以在命令行手动测试ADB连接：
    ```bash
    adb kill-server
    adb start-server
    adb devices
    # 如果是TCP/IP连接，可能需要先执行：
    # adb connect <TBOX_IP_ADDRESS>:<adb_port>
    ```

### 5. 串口权限

*   确保没有其他程序正在占用您配置的串口（例如 `COM15`）。
*   在Windows上，您可能需要以管理员权限运行脚本，以确保有足够的权限访问串口。

## 运行脚本

在项目根目录下打开终端，运行主程序：

```bash
python main.py
```

## 查看测试结果

每次测试运行都会在 `output_dir`（默认为 `./test_results`）下创建一个新的时间戳目录，例如 `test_results/20250526_135700`。

该目录中将包含：

*   程序运行日志 (`YYYYMMDD_HHMMSS_test_log.log`)
*   MCU串口日志 (`mcu_logs/YYYYMMDD_HHMMSS_mcu_log.txt`)
*   测试报告 (`YYYYMMDD_HHMMSS_test_report.txt`)
