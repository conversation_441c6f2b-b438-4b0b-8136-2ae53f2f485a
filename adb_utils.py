import hashlib
import os
import subprocess
import time

from loguru import logger


class AdbUtils:
    def __init__(
        self,
        adb_host,
        adb_port,
        adb_serial,
        adb_connect_timeout=10,
        adb_reconnect_interval=5,
    ):
        self.adb_host = adb_host
        self.adb_port = adb_port
        self.adb_serial = adb_serial
        self.adb_connect_timeout = adb_connect_timeout
        self.adb_reconnect_interval = adb_reconnect_interval

        if self.adb_serial:
            self.device_target = self.adb_serial  # 使用序列号
            logger.info(f"AdbUtils 初始化，目标设备序列号: {self.device_target}")
        elif self.adb_host and self.adb_port:
            self.device_target = f"{self.adb_host}:{self.adb_port}"  # 使用IP和端口
            logger.info(f"AdbUtils 初始化，目标设备地址: {self.device_target} (TCP/IP)")
        else:
            self.device_target = ""  # 不指定-s参数，让ADB自动选择（通常是USB）
            logger.info("AdbUtils 初始化，不指定设备 (ADB自动选择，通常是USB)")

    def _execute_adb_command(self, command, timeout=None):
        """
        执行ADB命令。
        """
        full_command = ["adb"]
        if self.device_target:  # 如果指定了设备目标，则使用-s参数
            full_command.extend(["-s", self.device_target])
        full_command.extend(command)

        logger.debug(f"执行ADB命令: {' '.join(full_command)}")
        try:
            result = subprocess.run(
                full_command,
                capture_output=True,  # 捕获stdout和stderr
                text=True,  # 将stdout和stderr解码为文本
                encoding="utf-8",  # 指定编码
                timeout=timeout,
                creationflags=subprocess.CREATE_NO_WINDOW,  # 不显示命令行窗口
            )

            if result.returncode == 0:
                stderr_lower = result.stderr.strip().lower()
                # 检查 stderr 是否有内容，并且不包含 push 命令的成功信息
                # 同时确保 stderr 不包含明确的错误词汇
                if (
                    stderr_lower
                    and not (
                        "file pushed" in stderr_lower and "skipped" in stderr_lower
                    )
                    and not (
                        "error" in stderr_lower
                        or "failed" in stderr_lower
                        or "permission denied" in stderr_lower
                    )
                ):
                    logger.error(
                        f"ADB命令成功但有错误输出 (stderr): {result.stderr.strip()}"
                    )
                    return False, result.stderr.strip()

                # 检查 stdout 是否包含常见的错误信息模式
                # 这种模式通常是 shell 内部命令的错误，但 adb shell 本身返回 0
                stdout_lower = result.stdout.strip().lower()

                # 针对 ls 和 rm 命令的特定错误模式
                if (
                    "ls:" in stdout_lower or "rm:" in stdout_lower
                ) and "no such file or directory" in stdout_lower:
                    logger.error(
                        f"ADB命令成功但 stdout 包含 'No such file or directory' 错误: {result.stdout.strip()}"
                    )
                    return False, result.stdout.strip()

                # 检查其他通用错误模式
                if "permission denied" in stdout_lower:
                    logger.error(
                        f"ADB命令成功但 stdout 包含 'Permission denied' 错误: {result.stdout.strip()}"
                    )
                    return False, result.stdout.strip()

                # 如果没有检测到错误，则认为是真正的成功
                logger.debug(f"ADB命令成功: {result.stdout.strip()}")
                return True, result.stdout.strip()
            else:
                error_output = (
                    result.stderr.strip()
                    if result.stderr.strip()
                    else result.stdout.strip()
                )
                logger.error(
                    f"ADB命令失败 (返回码: {result.returncode}): {error_output}"
                )
                return False, error_output
        except subprocess.TimeoutExpired:
            logger.error(f"ADB命令超时: {' '.join(full_command)}")
            return False, "Timeout"
        except FileNotFoundError:
            logger.error("ADB命令未找到。请确保ADB已安装并添加到系统PATH中。")
            return False, "ADB not found"
        except Exception as e:
            logger.error(f"执行ADB命令时发生错误: {e}")
            return False, str(e)

    def connect(self):
        """
        连接到ADB设备。
        如果指定了adb_host/adb_port (TCP/IP)，则尝试adb connect。
        如果指定了adb_serial或不指定设备，则直接检查设备是否已连接。
        """
        if self.adb_host and self.adb_port:  # TCP/IP连接
            logger.info(f"尝试连接到ADB设备: {self.device_target}")
            success, output = self._execute_adb_command(
                ["connect", self.device_target], timeout=self.adb_connect_timeout
            )
            if success and "connected to" in output:
                logger.info(f"成功连接到ADB设备: {self.device_target}")
                return True
            else:
                logger.warning(f"连接ADB设备失败: {output}")
                return False
        else:  # USB连接或不指定设备，直接检查是否已连接
            logger.info(
                f"尝试连接到ADB设备 {self.device_target if self.device_target else 'USB'}"
            )
            return self.is_connected()

    def disconnect(self):
        """
        断开ADB设备连接。
        如果指定了adb_host/adb_port (TCP/IP)，则尝试adb disconnect。
        如果指定了adb_serial或不指定设备，则不执行任何操作。
        """
        if self.adb_host and self.adb_port:  # TCP/IP连接
            logger.info(f"尝试断开ADB设备连接: {self.device_target}")
            success, output = self._execute_adb_command(
                ["disconnect", self.device_target]
            )
            if success:
                logger.info(f"成功断开ADB设备连接: {self.device_target}")
                return True
            else:
                logger.warning(f"断开ADB设备连接失败: {output}")
                return False
        else:  # USB连接或不指定设备，不执行disconnect
            logger.info(
                f"设备 {self.device_target if self.device_target else 'USB'} 无需执行adb disconnect命令。"
            )
            return True

    def is_connected(self):
        """
        检查ADB设备是否连接。
        """
        success, output = self._execute_adb_command(["devices"])
        if success:
            if self.device_target:  # 检查特定设备
                # 查找包含 device_target 且状态为 "device" 的行
                for line in output.splitlines():
                    if (
                        self.device_target in line
                        and "device" in line
                        and "offline" not in line
                    ):
                        logger.debug(f"ADB设备 {self.device_target} 已连接。")
                        return True
            else:  # 不指定设备，检查是否有任何设备连接
                # 排除 "List of devices attached" 和空行
                devices_list = [
                    line
                    for line in output.splitlines()
                    if line
                    and "device" in line
                    and "List of devices attached" not in line
                    and "offline" not in line
                ]
                if len(devices_list) > 0:
                    logger.debug("有ADB设备已连接。")
                    return True
        logger.debug(
            f"ADB设备 {self.device_target if self.device_target else '未指定'} 未连接。"
        )
        return False

    def wait_for_device(self, timeout=60):
        """
        等待设备连接。
        """
        logger.info(
            f"等待ADB设备 {self.device_target if self.device_target else '未指定'} 连接 (超时: {timeout}秒)..."
        )
        start_time = time.time()
        last_log_time = start_time

        while time.time() - start_time < timeout:
            if self.is_connected():
                elapsed = time.time() - start_time
                logger.info(
                    f"ADB设备 {self.device_target if self.device_target else '未指定'} 已连接 (等待了 {elapsed:.1f}秒)。"
                )
                return True

            # 每10秒输出一次等待状态
            current_time = time.time()
            if current_time - last_log_time >= 10:
                elapsed = current_time - start_time
                remaining = timeout - elapsed
                logger.info(f"仍在等待设备连接... 已等待 {elapsed:.1f}秒，剩余 {remaining:.1f}秒")
                last_log_time = current_time

            time.sleep(self.adb_reconnect_interval)

        logger.error(
            f"等待ADB设备 {self.device_target if self.device_target else '未指定'} 连接超时 ({timeout}秒)。"
        )
        return False

    def shell(self, command, timeout=30):
        """
        在ADB shell中执行命令。
        """
        logger.debug(f"在ADB shell中执行命令: {command}")
        success, output = self._execute_adb_command(["shell", command], timeout=timeout)
        return success, output

    def push_file(self, local_path, remote_path, max_retries=3, retry_wait_timeout=60):
        """
        将本地文件推送到远程设备。
        如果遇到设备未找到错误，会等待设备连接并重试。

        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径
            max_retries: 最大重试次数
            retry_wait_timeout: 每次重试前等待设备连接的超时时间(秒)
        """
        if not os.path.exists(local_path):
            logger.error(f"本地文件不存在: {local_path}")
            return False, "Local file not found"

        logger.info(f"推送文件 {local_path} 到 {remote_path}...")

        for attempt in range(max_retries):
            # 添加详细调试信息
            full_command = ["adb"]
            if self.device_target:
                full_command.extend(["-s", self.device_target])
            full_command.extend(["push", local_path, remote_path])
            logger.info(f"第 {attempt + 1}/{max_retries} 次尝试推送，即将执行的完整ADB命令: {' '.join(full_command)}")

            success, output = self._execute_adb_command(
                ["push", local_path, remote_path], timeout=300
            )  # 增加push超时时间

            # 添加更详细的调试信息
            logger.info(
                f"ADB push命令执行结果: success={success}, output_length={len(output) if output else 0}"
            )
            if output:
                logger.info(f"ADB push输出内容: '{output}'")
            else:
                logger.warning("ADB push输出为空")

            # 检查是否是设备未找到错误
            # 匹配常见的设备连接错误模式
            is_device_error = (not success and output and (
                ("device" in output.lower() and "not found" in output.lower()) or
                ("failed to get feature set" in output.lower()) or
                ("no devices/emulators found" in output.lower())
            ))

            if is_device_error:
                if attempt < max_retries - 1:  # 还有重试机会
                    logger.warning(f"设备未找到，第 {attempt + 1} 次推送失败: {output}")
                    logger.info(f"等待设备连接后重试 (超时: {retry_wait_timeout}秒)...")
                    if self.wait_for_device(timeout=retry_wait_timeout):
                        logger.info("设备已连接，准备重试推送...")
                        continue  # 重试
                    else:
                        logger.error(f"等待设备连接超时，放弃重试")
                        return False, f"设备连接超时，最后错误: {output}"
                else:
                    logger.error(f"已达到最大重试次数 ({max_retries})，推送失败: {output}")
                    return False, output

            if success:  # 只要ADB命令执行成功（返回码为0），就认为推送成功
                logger.info(f"文件 {local_path} 成功推送到 {remote_path}")
                # 额外检查：确认文件确实存在于远程设备
                if self.remote_file_exists(remote_path):
                    logger.info(f"远程文件 {remote_path} 存在，确认推送成功。")
                    return True, output
                else:
                    logger.error(
                        f"文件 {local_path} 推送成功但远程文件 {remote_path} 不存在。"
                    )
                    return False, "远程文件不存在"
            else:
                # 其他类型的错误，不重试
                logger.error(f"文件推送失败 (非设备连接问题): {output}")
                return False, output

        # 如果所有重试都失败了
        logger.error(f"推送文件失败，已尝试 {max_retries} 次")
        return False, f"推送失败，已重试 {max_retries} 次"

    def get_local_file_md5(self, file_path):
        """
        计算本地文件的MD5值。
        """
        if not os.path.exists(file_path):
            logger.error(f"本地文件不存在，无法计算MD5: {file_path}")
            return None

        logger.debug(f"计算本地文件MD5: {file_path}")
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            md5_value = hash_md5.hexdigest()
            logger.debug(f"本地文件 {file_path} 的MD5: {md5_value}")
            return md5_value
        except Exception as e:
            logger.error(f"计算本地文件MD5时发生错误: {e}")
            return None

    def get_remote_file_md5(self, remote_path):
        """
        通过ADB shell计算远程文件的MD5值。
        """
        logger.debug(f"计算远程文件MD5: {remote_path}")
        success, output = self.shell(f"md5sum {remote_path}")
        if success:
            md5_value = output.split(" ")[0]
            logger.debug(f"远程文件 {remote_path} 的MD5: {md5_value}")
            return md5_value
        else:
            logger.error(f"计算远程文件MD5失败: {output}")
            return None

    def compare_file_md5(self, local_path, remote_path):
        """
        比较本地文件和远程文件的MD5值。
        """
        local_md5 = self.get_local_file_md5(local_path)
        if local_md5 is None:
            return False, "无法获取本地文件MD5"

        remote_md5 = self.get_remote_file_md5(remote_path)
        if remote_md5 is None:
            return False, "无法获取远程文件MD5"

        if local_md5 == remote_md5:
            logger.info(f"本地文件和远程文件MD5一致: {local_md5}")
            return True, "MD5一致"
        else:
            logger.warning(
                f"本地文件MD5 ({local_md5}) 与远程文件MD5 ({remote_md5}) 不一致。"
            )
            return False, "MD5不一致"

    def remote_file_exists(self, remote_path):
        """
        检查远程文件是否存在。
        """
        logger.debug(f"检查远程文件是否存在: {remote_path}")
        # 使用 ls -l 命令来判断文件是否存在
        success, output = self.shell(f"ls -l {remote_path}")
        if success and remote_path in output:
            # ls -l 成功且输出中包含文件路径，说明文件存在
            logger.debug(f"远程文件 {remote_path} 存在。")
            return True
        # ls -l 失败或输出中不包含文件路径，说明文件不存在
        logger.debug(f"远程文件 {remote_path} 不存在。")
        return False

    def remote_dir_exists(self, remote_path):
        """
        检查远程目录是否存在。
        """
        logger.debug(f"检查远程目录是否存在: {remote_path}")
        success, _ = self.shell(f"test -d {remote_path}")
        if success:
            logger.debug(f"远程目录 {remote_path} 存在。")
            return True
        logger.debug(f"远程目录 {remote_path} 不存在。")
        return False

    def create_remote_dir(self, remote_path):
        """
        创建远程目录。
        """
        logger.info(f"尝试创建远程目录: {remote_path}")
        success, output = self.shell(f"mkdir -p {remote_path}")
        if success:
            logger.info(f"成功创建远程目录: {remote_path}")
            return True
        else:
            logger.error(f"创建远程目录失败: {output}")
            return False

    def delete_remote_file(self, remote_path):
        """
        删除远程文件。
        """
        logger.info(f"尝试删除远程文件: {remote_path}")
        # 尝试执行删除命令
        success, output = self.shell(f"rm {remote_path}")

        # 无论 rm 命令的直接返回结果如何，都再次检查文件是否存在
        if not self.remote_file_exists(remote_path):
            logger.info(f"成功删除远程文件: {remote_path} (或文件原本就不存在)")
            return True
        else:
            # 如果文件仍然存在，则删除失败
            logger.error(f"删除远程文件失败: {remote_path}. ADB命令输出: {output}")
            return False


if __name__ == "__main__":
    # 示例用法
    # 请确保ADB服务已启动，并且设备已连接
    # 例如：adb tcpip 5555; adb connect *************:5555

    # 导入日志配置
    import tempfile

    from logger_config import setup_logging

    # 创建一个临时日志目录
    temp_log_dir = os.path.join(tempfile.gettempdir(), "adb_test_logs")
    os.makedirs(temp_log_dir, exist_ok=True)
    setup_logging(temp_log_dir, {})

    # 示例1: TCP/IP连接
    logger.info("--- ADB Utils TCP/IP 示例开始 ---")
    adb_host_tcp = "*************"  # 替换为你的TBOX IP
    adb_port_tcp = 5555  # 替换为你的TBOX ADB端口
    adb_utils_tcp = AdbUtils(adb_host_tcp, adb_port_tcp, "")

    if not adb_utils_tcp.connect():
        logger.error("无法连接到ADB设备 (TCP/IP)，请检查设备连接和ADB服务。")
    else:
        logger.info("设备已连接 (TCP/IP)。")
        success, output = adb_utils_tcp.shell("ls /")
        if success:
            logger.info(f"ls / 命令输出:\n{output}")
        adb_utils_tcp.disconnect()
    logger.info("--- ADB Utils TCP/IP 示例结束 ---")

    # 示例2: USB连接 (通过序列号)
    logger.info("--- ADB Utils USB (序列号) 示例开始 ---")
    adb_serial_usb = "1234567890ABCDEF"  # 替换为你的设备序列号
    adb_utils_usb_serial = AdbUtils("", 0, adb_serial_usb)

    if not adb_utils_usb_serial.connect():  # 对于USB，connect只是检查是否已连接
        logger.error("无法连接到ADB设备 (USB序列号)，请检查设备是否已通过USB连接。")
    else:
        logger.info("设备已连接 (USB序列号)。")
        success, output = adb_utils_usb_serial.shell("ls /")
        if success:
            logger.info(f"ls / 命令输出:\n{output}")
        adb_utils_usb_serial.disconnect()  # 对于USB，disconnect不执行任何操作
    logger.info("--- ADB Utils USB (序列号) 示例结束 ---")

    # 示例3: USB连接 (不指定序列号，ADB自动选择)
    logger.info("--- ADB Utils USB (自动选择) 示例开始 ---")
    adb_utils_usb_auto = AdbUtils("", 0, "")

    if not adb_utils_usb_auto.connect():  # 对于USB，connect只是检查是否已连接
        logger.error("无法连接到ADB设备 (USB自动选择)，请检查设备是否已通过USB连接。")
    else:
        logger.info("设备已连接 (USB自动选择)。")
        success, output = adb_utils_usb_auto.shell("ls /")
        if success:
            logger.info(f"ls / 命令输出:\n{output}")
        adb_utils_usb_auto.disconnect()  # 对于USB，disconnect不执行任何操作
    logger.info("--- ADB Utils USB (自动选择) 示例结束 ---")

    # 更多文件操作示例 (使用TCP/IP连接为例)
    if adb_utils_tcp.connect():
        temp_file_content = "Hello, ADB Test!"
        temp_local_file = os.path.join(temp_log_dir, "test_file.txt")
        with open(temp_local_file, "w") as f:
            f.write(temp_file_content)
        logger.info(f"创建本地临时文件: {temp_local_file}")

        remote_test_dir = "/data/local/tmp/autoupgrade_test"
        remote_test_file = os.path.join(remote_test_dir, "test_file.txt").replace(
            "\\", "/"
        )  # 确保路径是Unix风格

        if not adb_utils_tcp.remote_dir_exists(remote_test_dir):
            adb_utils_tcp.create_remote_dir(remote_test_dir)

        success, msg = adb_utils_tcp.push_file(temp_local_file, remote_test_file)
        if success:
            logger.info(f"文件推送成功: {msg}")
            if adb_utils_tcp.remote_file_exists(remote_test_file):
                logger.info(f"远程文件 {remote_test_file} 存在。")
                md5_match, md5_msg = adb_utils_tcp.compare_file_md5(
                    temp_local_file, remote_test_file
                )
                logger.info(f"MD5比较结果: {md5_msg}")
            else:
                logger.error(f"远程文件 {remote_test_file} 不存在。")
        else:
            logger.error(f"文件推送失败: {msg}")

        if adb_utils_tcp.remote_file_exists(remote_test_file):
            adb_utils_tcp.delete_remote_file(remote_test_file)
            if not adb_utils_tcp.remote_file_exists(remote_test_file):
                logger.info(f"远程文件 {remote_test_file} 已成功删除。")
            else:
                logger.error(f"远程文件 {remote_test_file} 删除失败。")
        adb_utils_tcp.disconnect()
