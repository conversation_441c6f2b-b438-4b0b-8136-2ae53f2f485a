# 设备等待机制修复说明

## 问题描述

根据日志分析，在自动升级测试过程中，当进行下一轮测试时，设备有概率还未完全启动，导致ADB命令执行时出现"device not found"错误。原有代码在遇到此错误时会立即失败并切换到下一次测试，没有等待设备连接的机制。

## 错误日志示例

```
2025-07-21 10:49:22.522 | ERROR    | adb_utils:_execute_adb_command:103 - ADB命令失败 (返回码: 1): adb: error: failed to get feature set: device '1234567890ABCDEF' not found
2025-07-21 10:49:22.523 | ERROR    | test_runner:_run_single_upgrade_test:121 - 阶段1失败: 推送升级包失败: adb: error: failed to get feature set: device '1234567890ABCDEF' not found
```

## 修复方案

### 1. 测试运行器修改 (test_runner.py)

在推送升级包之前，增加设备连接等待步骤：

```python
# 在推送前先确保设备已连接，等待设备启动
logger.info("1.2.1 等待设备连接...")
if not self.adb_utils.wait_for_device(timeout=120):  # 等待2分钟设备连接
    error_message = "阶段1失败: 等待设备连接超时，设备可能未启动"
    logger.error(error_message)
    return False, 0, test_start_time, datetime.now(), error_message
logger.info("1.2.1 设备已连接。")
```

**修改位置**: `test_runner.py` 第156-173行

### 2. ADB工具类增强 (adb_utils.py)

#### 2.1 增强push_file方法

- 添加重试机制，当遇到设备连接错误时自动重试
- 新增参数：
  - `max_retries`: 最大重试次数 (默认3次)
  - `retry_wait_timeout`: 每次重试前等待设备连接的超时时间 (默认60秒)
- 增强错误检测，支持多种设备连接错误模式：
  - `device 'xxx' not found`
  - `failed to get feature set`
  - `no devices/emulators found`

**修改位置**: `adb_utils.py` 第225-310行

#### 2.2 增强wait_for_device方法

- 添加更详细的等待状态日志
- 每10秒输出一次等待进度
- 显示已等待时间和剩余时间
- 显示实际等待时长

**修改位置**: `adb_utils.py` 第197-228行

## 修复效果

1. **提高测试稳定性**: 当设备未完全启动时，系统会等待设备连接而不是立即失败
2. **智能重试机制**: push_file方法能够自动检测设备连接问题并重试
3. **更好的日志信息**: 提供详细的等待状态和重试信息，便于问题诊断
4. **向后兼容**: 所有新增参数都有默认值，不影响现有代码

## 配置建议

### 超时时间设置

- **设备连接等待**: 120秒 (考虑设备重启时间)
- **推送重试等待**: 60秒 (每次重试前的等待时间)
- **最大重试次数**: 3次

### 日志监控

修复后的日志会包含以下信息：
- 设备连接等待状态
- 推送重试次数和原因
- 详细的时间统计

## 测试验证

可以使用提供的测试脚本验证修复效果：

```bash
python test_device_wait.py
```

该脚本会测试：
1. 设备连接检查
2. 等待设备连接功能
3. 文件推送重试机制

## 注意事项

1. 增加的等待时间可能会延长单次测试的总时间
2. 如果设备硬件故障，等待机制无法解决根本问题
3. 建议在生产环境中根据实际设备启动时间调整超时参数
