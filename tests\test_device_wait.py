#!/usr/bin/env python3
"""
测试设备等待功能的脚本
用于验证修复后的ADB设备连接等待逻辑
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # 从tests目录回到项目根目录
sys.path.insert(0, str(project_root))

from adb_utils import AdbUtils
from logger_config import setup_logging


def test_device_wait():
    """测试设备等待功能"""
    
    # 设置日志
    temp_log_dir = os.path.join(tempfile.gettempdir(), "device_wait_test_logs")
    os.makedirs(temp_log_dir, exist_ok=True)
    setup_logging(temp_log_dir, {})
    
    print("=== 设备等待功能测试 ===")
    print(f"日志目录: {temp_log_dir}")
    
    # 使用配置中的设备信息进行测试
    # 这里使用一个不存在的设备序列号来模拟设备未连接的情况
    test_serial = "1234567890ABCDEF"  # 从日志中看到的序列号
    
    print(f"测试设备序列号: {test_serial}")
    
    # 创建ADB工具实例
    adb_utils = AdbUtils("", 0, test_serial)
    
    print("\n1. 测试设备连接检查...")
    if adb_utils.is_connected():
        print("✓ 设备已连接")
    else:
        print("✗ 设备未连接")
    
    print("\n2. 测试等待设备连接 (30秒超时)...")
    start_time = time.time()
    if adb_utils.wait_for_device(timeout=30):
        elapsed = time.time() - start_time
        print(f"✓ 设备连接成功 (等待了 {elapsed:.1f}秒)")
        
        print("\n3. 测试文件推送...")
        # 创建一个测试文件
        test_file = os.path.join(temp_log_dir, "test_push.txt")
        with open(test_file, "w") as f:
            f.write("Test content for device wait verification")
        
        # 尝试推送文件
        remote_path = "/data/local/tmp/test_push.txt"
        success, output = adb_utils.push_file(test_file, remote_path)
        if success:
            print(f"✓ 文件推送成功: {output}")
            
            # 清理测试文件
            if adb_utils.remote_file_exists(remote_path):
                adb_utils.delete_remote_file(remote_path)
                print("✓ 测试文件已清理")
        else:
            print(f"✗ 文件推送失败: {output}")
    else:
        elapsed = time.time() - start_time
        print(f"✗ 设备连接超时 (等待了 {elapsed:.1f}秒)")
        
        print("\n3. 测试推送文件的重试机制...")
        # 创建一个测试文件
        test_file = os.path.join(temp_log_dir, "test_push_retry.txt")
        with open(test_file, "w") as f:
            f.write("Test content for retry mechanism")
        
        # 尝试推送文件，应该会触发重试机制
        remote_path = "/data/local/tmp/test_push_retry.txt"
        success, output = adb_utils.push_file(
            test_file, remote_path, 
            max_retries=2, 
            retry_wait_timeout=15
        )
        if success:
            print(f"✓ 文件推送成功 (通过重试): {output}")
        else:
            print(f"✗ 文件推送失败 (即使重试): {output}")
    
    print(f"\n测试完成。详细日志请查看: {temp_log_dir}")


if __name__ == "__main__":
    test_device_wait()
