import threading
import time
from datetime import datetime
from pathlib import Path

import serial
from loguru import logger


class SerialMonitor:
    def __init__(
        self, port, baudrate, bytesize, parity, stopbits, timeout, log_dir: Path
    ):
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.timeout = timeout
        self.log_dir = log_dir  # log_dir is already a Path object
        self.serial_port = None
        self.running = False
        self.thread = None
        self.log_file_path: Path = log_dir / datetime.now().strftime(
            "%Y%m%d_%H%M%S_mcu_log.txt"
        )
        logger.info(
            f"SerialMonitor 初始化，串口配置: {port}, {baudrate}, {bytesize}, {parity}, {stopbits}, {timeout}"
        )

    def _get_serial_config(self):
        """根据字符串获取pyserial的配置枚举"""
        bytesize_map = {
            5: serial.FIVEBITS,
            6: serial.SIXBITS,
            7: serial.SEVENBITS,
            8: serial.EIGHTBITS,
        }
        parity_map = {
            "N": serial.PARITY_NONE,
            "E": serial.PARITY_EVEN,
            "O": serial.PARITY_ODD,
            "M": serial.PARITY_MARK,
            "S": serial.PARITY_SPACE,
        }
        stopbits_map = {
            1: serial.STOPBITS_ONE,
            1.5: serial.STOPBITS_ONE_POINT_FIVE,
            2: serial.STOPBITS_TWO,
        }

        return {
            "port": self.port,
            "baudrate": self.baudrate,
            "bytesize": bytesize_map.get(self.bytesize, serial.EIGHTBITS),
            "parity": parity_map.get(self.parity.upper(), serial.PARITY_NONE),
            "stopbits": stopbits_map.get(self.stopbits, serial.STOPBITS_ONE),
            "timeout": self.timeout,
        }

    def start_monitoring(self):
        """
        开始监控串口。
        """
        if self.running:
            logger.warning("串口监控已在运行中。")
            return

        try:
            serial_config = self._get_serial_config()
            self.serial_port = serial.Serial(**serial_config)
            logger.info(f"成功打开串口: {self.port}")
        except serial.SerialException as e:
            logger.error(f"打开串口 {self.port} 失败: {e}")
            return

        self.log_dir.mkdir(parents=True, exist_ok=True)
        log_file_name = datetime.now().strftime("%Y%m%d_%H%M%S_mcu_log.txt")
        self.log_file_path = self.log_dir / log_file_name

        self.running = True
        self.thread = threading.Thread(target=self._read_serial_data, daemon=True)
        self.thread.start()
        logger.info(
            f"开始监控串口 {self.port}，日志将保存到: {self.log_file_path.as_posix()}"
        )

    def stop_monitoring(self):
        """
        停止监控串口。
        """
        if not self.running:
            logger.warning("串口监控未在运行中。")
            return

        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)  # 等待线程结束
            if self.thread.is_alive():
                logger.warning("串口读取线程未能正常停止。")

        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            logger.info(f"串口 {self.port} 已关闭。")
        logger.info("串口监控已停止。")

    def _read_serial_data(self):
        """
        在单独的线程中读取串口数据并写入文件。
        """
        if not self.log_file_path:
            logger.error("Log file path is not set")
            return

        try:
            with open(
                self.log_file_path.as_posix(), "a", encoding="utf-8", errors="ignore"
            ) as f:
                while self.running:
                    if (
                        self.serial_port
                        and self.serial_port.is_open
                        and self.serial_port.in_waiting > 0
                    ):
                        try:
                            data = self.serial_port.read(
                                self.serial_port.in_waiting
                            ).decode("utf-8", errors="ignore")
                            f.write(
                                f"{data.strip()}\n" if data.endswith("\n") else data
                            )
                            f.flush()  # 实时写入文件
                            # logger.debug(f"读取到串口数据: {data.strip()}") # 调试时可以打开
                        except Exception as e:
                            logger.error(f"读取或写入串口数据时发生错误: {e}")
                    time.sleep(0.01)  # 短暂休眠，避免CPU占用过高
        except Exception as e:
            logger.error(f"串口监控线程发生未预期错误: {e}")
        finally:
            logger.info("串口读取线程退出。")


if __name__ == "__main__":
    # 示例用法
    import tempfile

    from logger_config import setup_logging

    # 创建一个临时日志目录
    temp_log_dir = Path(tempfile.gettempdir()) / "serial_test_logs"  # Use Path
    temp_log_dir.mkdir(parents=True, exist_ok=True)  # Use Path.mkdir
    setup_logging(temp_log_dir.as_posix(), {})  # setup_logging expects a string path

    # 假设你的串口是COM1，波特率115200
    # 请根据实际情况修改串口参数
    serial_port_name = "COM1"  # 或者 "/dev/ttyUSB0" on Linux
    baud_rate = 115200
    bytes_size = 8
    parity_type = "N"
    stop_bits = 1
    read_timeout = 1

    monitor = SerialMonitor(
        port=serial_port_name,
        baudrate=baud_rate,
        bytesize=bytes_size,
        parity=parity_type,
        stopbits=stop_bits,
        timeout=read_timeout,
        log_dir=temp_log_dir,  # Pass as Path object
    )

    logger.info("--- 串口监控示例开始 ---")
    monitor.start_monitoring()

    logger.info("监控中，等待10秒...")
    time.sleep(10)  # 模拟程序运行10秒

    monitor.stop_monitoring()
    logger.info("--- 串口监控示例结束 ---")
    logger.info(
        f"MCU日志已保存到: {monitor.log_file_path.as_posix()}"
    )  # Use as_posix()
