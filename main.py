from pathlib import Path

from loguru import logger

from test_runner import TestRunner


def main():
    # 1. 加载初始配置，获取output_dir
    # 用户需要复制 config.json.template 为 config.json 并进行配置
    config_template_path = Path("config.json.template")
    user_config_path = Path("config.json")

    if not user_config_path.exists():
        print(
            f"错误: 用户配置文件 {user_config_path.as_posix()} 不存在。请复制 {config_template_path.as_posix()} 为 {user_config_path.as_posix()} 并进行配置。"
        )
        return

    # 2. TestRunner内部会处理output_dir的创建和日志配置
    logger.info("程序启动。")

    try:
        # 3. 初始化并运行测试
        runner = TestRunner(config_path=user_config_path.as_posix())
        runner.run_tests()
        logger.info("测试执行完毕。")
    except KeyboardInterrupt:
        logger.info("用户中断测试。TestRunner内部已处理清理和报告生成。")
    except Exception as e:
        logger.critical(f"程序运行过程中发生严重错误: {e}", exc_info=True)
    finally:
        logger.info("程序退出。")


if __name__ == "__main__":
    main()
