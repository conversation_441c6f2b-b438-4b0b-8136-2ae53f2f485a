from datetime import datetime
from pathlib import Path

from loguru import logger
import json  # 需要导入json模块来读取config.json


def setup_logging(output_dir, config_data):  # 增加config_data参数
    """
    配置loguru日志。
    日志将输出到指定目录下的一个时间戳命名的文件。
    日志级别从config_data中读取。
    """
    log_file_name = datetime.now().strftime("%Y%m%d_%H%M%S_test_log.log")
    output_path = Path(output_dir)
    log_file_path = output_path / log_file_name

    file_log_level = config_data.get("logging", {}).get("file_level", "DEBUG")
    console_log_level = config_data.get("logging", {}).get("console_level", "INFO")

    # 移除默认的控制台输出处理器
    logger.remove()

    # 添加文件输出处理器
    logger.add(
        log_file_path.as_posix(),
        rotation="10 MB",
        compression="zip",
        level=file_log_level,  # 从配置中读取
        encoding="utf-8",
        enqueue=True,
        retention="7 days",
    )

    # 也可以添加一个控制台输出，方便实时查看
    logger.add(
        lambda msg: print(msg, end=""),
        level=console_log_level,  # 从配置中读取
        colorize=True,
    )

    logger.info(f"日志已配置，输出到: {log_file_path.as_posix()}")
    return log_file_path.as_posix()


if __name__ == "__main__":
    # 示例用法
    output_test_dir = Path("./test_logs_example")
    output_test_dir.mkdir(parents=True, exist_ok=True)

    # 加载config.json
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
    except FileNotFoundError:
        print("config.json not found. Using default logging levels.")
        config = {}  # 或者加载config.json.template作为默认配置
    except json.JSONDecodeError:
        print("Error decoding config.json. Using default logging levels.")
        config = {}

    setup_logging(output_test_dir.as_posix(), config)
    logger.info("这是一个测试日志消息。")
    logger.debug("这是一个调试消息，如果级别设置为DEBUG则可见。")
    logger.warning("这是一个警告消息。")
    logger.error("这是一个错误消息。")
